package com.stock.service.platform.compliance.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 聊天反馈DTO
 */
@Data
public class ChatFeedbackDto implements Serializable {

    /**
     * 主键
     */
    private String id;

    /**
     * 对话id
     */
    private String chatContentId;

    /**
     * 问题
     */
    private String question;

    /**
     * 回复
     */
    private String answer;

    /**
     * 反馈类型(回答有误:1    响应慢:2    案例有误:3    法规有误:4    其他:0)
     */
    private String feedbackType;

    /**
     * 用户姓名
     */
    private String feedbackName;

    /**
     * 联系方式
     */
    private String telephone;

    /**
     * 反馈内容
     */
    private String feedbackContent;

    /**
     * 最新答复
     */
    private String newReply;

    /**
     * 最新答复时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date newReplyTime;

    /**
     * 是否答复(未答复:0    已答复:1)
     */
    private String isReply;

    /**
     * 答复new标识(用户反馈列表用,不显示:0    显示:1)
     */
    private String isReplyNew;

    /**
     * 用户ID
     */
    private String userName;

    /**
     * 答复人
     */
    private String replyName;

    /**
     * 反馈new标识(管理员列表用,不显示:0    显示:1)
     */
    private String isFeedbackNew;

    /**
     * 最新反馈
     */
    private String newFeedback;

    /**
     * 最新反馈时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date newFeedbackTime;

    /**
     * 是否有效(无效:0    有效:1)
     */
    private String status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 问题内容（用于前端显示）
     */
    private String questionContent;

    /**
     * 首次提问时间（用于前端显示）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date firstQuestionTime;

    private static final long serialVersionUID = 1L;
}
